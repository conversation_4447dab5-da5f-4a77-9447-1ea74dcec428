import { defineStore } from 'pinia'

// 定义示意图项目接口
interface SchematicItem {
  name: string
  icon: string | null
  desc: string
}

// 定义示意图信息接口
interface SchematicInfo {
  count: number
  fileName: string
  nums: string
  title: string
  type: string
  items: SchematicItem[]
  svgHeight?: number
  svgUrl?: string
  svgWidth?: number
  docId?: string
}

interface SvgStyleInfo {
  count: number
  fileName: string
  nums: string
  type: string
}

// 定义 store 的状态接口
interface ISchematicSvgState {
  visible: boolean
  svgStyleList: SvgStyleInfo[]
  schematicData: SchematicInfo | undefined
  isEdit: boolean
}

// 使用对象语法定义 store
export const useSchematicSvgStore = defineStore({
  id: 'schematicSvg',

  state: (): ISchematicSvgState => ({
    visible: false,
    svgStyleList: [],
    schematicData: undefined,
    isEdit: false
  }),

  getters: {
    // 获取示意图标题
    getTitle: (state): string => {
      return state.schematicData?.title || '示意图预览'
    },

    // 获取示意图类型
    getType: (state): string => {
      return state.schematicData?.type || ''
    },

    // 获取示意图项目
    getItems: (state): SchematicItem[] => {
      return state.schematicData?.items || []
    }
  },

  actions: {
    // 打开示意图预览弹窗
    openModal() {
      this.visible = true
    },

    // 关闭示意图预览弹窗
    closeModal() {
      this.visible = false
    },

    // 清除示意图数据
    clearSchematicData() {
      this.schematicData = undefined
    },

    // 设置示意图数据
    setSchematicData(data: SchematicInfo) {
      this.schematicData = data
    },

    // 设置是否编辑
    setIsEdit(isEdit: boolean) {
      this.isEdit = isEdit
    },

    // 设置示意图样式数据
    setSvgStyleData(data: SvgStyleInfo[]) {
      this.svgStyleList = data
    }
  }
})
